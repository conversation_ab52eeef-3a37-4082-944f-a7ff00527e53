-- ============================================================================
-- PLUGIN MANAGEMENT AND LOADING
-- ============================================================================
-- This file manages plugin installation and loading using vim.pack

local M = {}

-- ============================================================================
-- MINI.NVIM BOOTSTRAP
-- ============================================================================

local function bootstrap_mini()
    local path_package = vim.fn.stdpath('data') .. '/site'
    local mini_path = path_package .. '/pack/deps/start/mini.nvim'

    if not vim.loop.fs_stat(mini_path) then
        vim.cmd('echo "Installing `mini.nvim`" | redraw')
        local clone_cmd = {
            'git', 'clone', '--filter=blob:none',
            -- Uncomment next line to use 'stable' branch
            -- '--branch', 'stable',
            'https://github.com/echasnovski/mini.nvim', mini_path
        }
        vim.fn.system(clone_cmd)
        vim.cmd('packadd mini.nvim | helptags ALL')
        vim.cmd('echo "Installed `mini.nvim`" | redraw')
    end
end

-- ============================================================================
-- PLUGIN DECLARATIONS
-- ============================================================================

local function setup_plugins()
    vim.pack.add({
        -- Core dependencies
        { src = "https://github.com/nvim-lua/plenary.nvim" },
        { src = "https://github.com/MunifTanjim/nui.nvim" },
        { src = "https://github.com/kevinhwang91/promise-async" },

        -- Mini.nvim suite
        { src = "https://github.com/echasnovski/mini.nvim" },

        -- UI and Dashboard
        { src = "https://github.com/folke/snacks.nvim" },
        { src = "https://github.com/folke/which-key.nvim" },
        { src = "https://github.com/folke/noice.nvim" },

        -- File management and navigation
        { src = "https://github.com/stevearc/oil.nvim" },
        { src = "https://github.com/folke/flash.nvim" },
        { src = "https://github.com/nvim-telescope/telescope.nvim" },
        { src = "https://github.com/nvim-telescope/telescope-fzf-native.nvim" },

        -- Treesitter and syntax
        { src = "https://github.com/nvim-treesitter/nvim-treesitter" },
        { src = "https://github.com/nvim-treesitter/nvim-treesitter-textobjects" },

        -- LSP and completion
        { src = "https://github.com/neovim/nvim-lspconfig" },
        { src = "https://github.com/mason-org/mason.nvim" },
        { src = "https://github.com/mason-org/mason-lspconfig.nvim" },
        { src = "https://github.com/WhoIsSethDaniel/mason-tool-installer.nvim" },
        { src = "https://github.com/saghen/blink.cmp", checkout = "v0.*" },

        -- Git integration
        { src = "https://github.com/tpope/vim-fugitive" },

        -- Editor enhancements
        { src = "https://github.com/ThePrimeagen/harpoon" },
        { src = "https://github.com/mbbill/undotree" },
        { src = "https://github.com/kevinhwang91/nvim-ufo" },

        -- Debugging
        { src = "https://github.com/mfussenegger/nvim-dap" },
        { src = "https://github.com/nvim-telescope/telescope-dap.nvim" },

        -- Themes
        { src = "https://github.com/vague2k/vague.nvim" },
        { src = "https://github.com/catppuccin/nvim" },
        { src = "https://github.com/rebelot/kanagawa.nvim" },

        -- Language specific
        { src = "https://github.com/chomosuke/typst-preview.nvim" },

        -- Laravel Development
        { src = "https://github.com/adalessa/laravel.nvim" },
        { src = "https://github.com/ricardoramirezr/blade-nav.nvim" },
        { src = "https://github.com/jwalton512/vim-blade" },

        -- AI Assistance
        { src = "https://github.com/github/copilot.vim" },
        { src = "https://github.com/augmentcode/augment.vim" },
        { src = "https://github.com/Exafunction/codeium.nvim" },
        { src = "https://github.com/olimorris/codecompanion.nvim" },
        { src = "https://github.com/David-Kunz/gen.nvim" },

        -- Note taking
        { src = "https://github.com/nvim-neorg/neorg" },

        -- Command line enhancement
        { src = "https://github.com/gelguy/wilder.nvim" },

        -- Telescope extensions
        { src = "https://github.com/jvgrootveld/telescope-zoxide" },
        { src = "https://github.com/nvim-neorg/neorg-telescope" },
        { src = "https://github.com/gbirke/telescope-foldmarkers.nvim" },
        { src = "https://github.com/zschreur/telescope-jj.nvim" },
        { src = "https://github.com/nvim-telescope/telescope-github.nvim" },
        { src = "https://github.com/nvim-telescope/telescope-media-files.nvim" },
        { src = "https://github.com/nvim-telescope/telescope-fzf-writer.nvim" },
        { src = "https://github.com/nvim-telescope/telescope-symbols.nvim" },
        { src = "https://github.com/olacin/telescope-cc.nvim" },
        { src = "https://github.com/sudormrfbin/cheatsheet.nvim" },
        { src = "https://github.com/nat-418/telescope-color-names.nvim" },
        { src = "https://github.com/octarect/telescope-menu.nvim" },
        { src = "https://github.com/debugloop/telescope-undo.nvim" },
    })
end

-- ============================================================================
-- PLUGIN CONFIGURATION LOADER
-- ============================================================================

local function load_plugin_configs()
    -- Load essential plugins first
    vim.cmd('packadd plenary.nvim')
    vim.cmd('packadd nui.nvim')
    vim.cmd('packadd promise-async')

    -- Load plugin configurations in order
    -- UI Components
    require("plugins.ui.telescope")
    require("plugins.ui.dashboard")
    require("plugins.ui.which-key")
    require("plugins.ui.noice")

    -- LSP and Development Tools
    require("plugins.lsp")

    -- Editor Enhancements
    require("plugins.editor.treesitter")
    require("plugins.editor.oil")
    require("plugins.editor.flash")
    require("plugins.editor.ufo")
    require("plugins.editor.undotree")

    -- Git Integration
    require("plugins.git.fugitive")

    -- Debug Tools
    require("plugins.debug.dap")

    -- Language Specific
    require("plugins.lang.php")

    -- AI Assistance
    require("plugins.ai.copilot")
    require("plugins.ai.codeium")
    require("plugins.ai.augment")
end

-- ============================================================================
-- MAIN SETUP FUNCTION
-- ============================================================================

function M.setup()
    -- Bootstrap mini.nvim first
    bootstrap_mini()

    -- Setup plugins
    setup_plugins()

    -- Load plugin configurations
    load_plugin_configs()
end

return M
