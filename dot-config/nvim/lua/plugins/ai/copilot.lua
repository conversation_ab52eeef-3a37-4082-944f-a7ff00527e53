-- ============================================================================
-- GITHUB COPILOT CONFIGURATION
-- ============================================================================
-- AI assistance configuration

-- GitHub Copilot works out of the box with minimal configuration
-- The plugin is loaded automatically when installed

-- ============================================================================
-- COPILOT KEYMAPS
-- ============================================================================

-- Basic Copilot keymaps (if needed)
-- Most Copilot functionality works through Tab completion
-- vim.keymap.set('i', '<C-J>', 'copilot#Accept("\\<CR>")', { expr = true, silent = true })
-- vim.keymap.set('i', '<C-]>', 'copilot#Dismiss()', { expr = true, silent = true })

-- Note: GitHub Copilot typically doesn't require explicit setup
-- It works automatically once authenticated with :Copilot setup
