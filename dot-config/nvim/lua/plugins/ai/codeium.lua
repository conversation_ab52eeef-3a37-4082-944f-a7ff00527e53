-- ============================================================================
-- CODEIUM AI CONFIGURATION
-- ============================================================================
-- Codeium AI assistance configuration

-- Codeium.nvim setup
require("codeium").setup({
    -- Enable codeium chat
    enable_chat = true,
    -- Disable nvim-cmp integration since we're using blink.cmp
    enable_cmp_source = false,
})

-- ============================================================================
-- CODEIUM KEYMAPS
-- ============================================================================

-- Codeium keymaps
vim.keymap.set('i', '<C-g>', function () return vim.fn['codeium#Accept']() end, { expr = true, silent = true })
vim.keymap.set('i', '<c-;>', function() return vim.fn['codeium#CycleCompletions'](1) end, { expr = true, silent = true })
vim.keymap.set('i', '<c-,>', function() return vim.fn['codeium#CycleCompletions'](-1) end, { expr = true, silent = true })
vim.keymap.set('i', '<c-x>', function() return vim.fn['codeium#Clear']() end, { expr = true, silent = true })

-- Chat functionality
vim.keymap.set('n', '<leader>cc', function() require('codeium.chat').open() end, { desc = 'Open Codeium Chat' })
