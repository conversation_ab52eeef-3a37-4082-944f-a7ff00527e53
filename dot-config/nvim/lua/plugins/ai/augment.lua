-- ============================================================================
-- AUGMENT AI CONFIGURATION
-- ============================================================================
-- Augment AI assistance configuration

-- Augment.vim typically works out of the box
-- Configuration can be added here if needed

-- ============================================================================
-- AUGMENT KEYMAPS
-- ============================================================================

-- Augment keymaps (if any specific ones are needed)
-- Most Augment functionality is integrated into the editor automatically
-- vim.keymap.set('n', '<leader>ai', '<cmd>AugmentToggle<cr>', { desc = 'Toggle Augment AI' })
