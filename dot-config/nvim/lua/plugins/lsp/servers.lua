-- ============================================================================
-- LSP SERVER CONFIGURATIONS
-- ============================================================================
-- Individual LSP server configurations

local lspconfig = require('lspconfig')

-- ============================================================================
-- LUA LSP CONFIGURATION
-- ============================================================================

lspconfig.lua_ls.setup({
    settings = {
        Lua = {
            runtime = {
                version = 'LuaJIT',
            },
            diagnostics = {
                globals = {
                    'vim',
                    'require'
                },
            },
            workspace = {
                library = vim.api.nvim_get_runtime_file("", true),
            },
            telemetry = {
                enable = false,
            },
        },
    },
})

-- ============================================================================
-- PHP LSP CONFIGURATION
-- ============================================================================

-- Manual PHP LSP configuration for Devsense PHP LS
vim.api.nvim_create_autocmd("FileType", {
    pattern = "php",
    callback = function()
        vim.lsp.start({
            name = "devsense-php-ls",
            cmd = { "devsense-php-ls" }, -- Adjust this if the command is different
            root_dir = vim.fs.dirname(vim.fs.find({"composer.json", ".git"}, { upward = true })[1]),
            settings = {
                php = {
                    completion = {
                        enabled = true,
                    },
                    diagnostics = {
                        enabled = true,
                    },
                    format = {
                        enabled = true,
                    },
                },
            },
        })
    end,
})

-- ============================================================================
-- OTHER LSP SERVERS
-- ============================================================================

-- Add other LSP server configurations here as needed
-- They will be automatically installed by Mason if listed in mason.lua
