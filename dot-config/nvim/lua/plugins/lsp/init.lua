-- ============================================================================
-- LSP CONFIGURATION
-- ============================================================================
-- Language Server Protocol configuration and setup

-- Load LSP modules
require("plugins.lsp.mason")
require("plugins.lsp.servers")
require("plugins.lsp.completion")

-- ============================================================================
-- LSP KEYMAPS
-- ============================================================================

-- LSP mappings
vim.keymap.set('n', '<leader>lf', vim.lsp.buf.format, { desc = 'Format document' })

-- Mason mappings
vim.keymap.set('n', '<leader>lm', '<cmd>Mason<cr>', { desc = 'Mason' })
vim.keymap.set('n', '<leader>li', '<cmd>MasonInstall ', { desc = 'Mason install' })
vim.keymap.set('n', '<leader>lu', '<cmd>MasonUpdate<cr>', { desc = 'Mason update' })
vim.keymap.set('n', '<leader>ls', '<cmd>MasonUninstall ', { desc = 'Mason uninstall' })
