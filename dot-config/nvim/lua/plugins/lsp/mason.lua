-- ============================================================================
-- MASON CONFIGURATION
-- ============================================================================
-- Mason setup for LSP server management

-- Mason setup for LSP server management
require('mason').setup({
    ui = {
        border = "rounded",
        icons = {
            package_installed = "✓",
            package_pending = "➜",
            package_uninstalled = "✗"
        }
    }
})

-- Mason-lspconfig setup for automatic LSP server installation
require('mason-lspconfig').setup({
    ensure_installed = {
        "lua_ls",
        "biome",
        "tinymist",
        "emmet_ls", -- Note: emmet_ls in Mason, not emmetls
        "phpactor", -- PHP LSP server
        "tailwindcss", -- Tailwind CSS LSP server
    },
    automatic_installation = true,
})

-- Mason-tool-installer for additional tools
require('mason-tool-installer').setup({
    ensure_installed = {
        "lua_ls",
        "stylua", -- Lua formatter
        "biome",
        "tinymist",
        "emmet_ls",
        "phpactor", -- PHP LSP server
        "tailwindcss", -- Tailwind CSS LSP server
    },
    auto_update = true, -- Set to true if you want automatic updates
    run_on_start = true,
})
