-- ============================================================================
-- FLASH NAVIGATION CONFIGURATION
-- ============================================================================
-- Enhanced jump/search with labels

require("flash").setup({
    -- Flash configuration
    labels = "asdfghjklqwertyuiopzxcvbnm",
    search = {
        multi_window = true,
        forward = true,
        wrap = true,
        mode = "exact",
        incremental = false,
        exclude = {
            "notify",
            "cmp_menu",
            "noice",
            "flash_prompt",
        },
    },
    jump = {
        jumplist = true,
        pos = "start",
        history = false,
        register = false,
        nohlsearch = false,
        autojump = false,
    },
    label = {
        uppercase = true,
        exclude = "",
        current = true,
        after = true,
        before = false,
        style = "overlay",
        reuse = "lowercase",
        distance = true,
        min_pattern_length = 0,
        rainbow = {
            enabled = false,
            shade = 5,
        },
    },
    highlight = {
        backdrop = true,
        matches = true,
        priority = 5000,
        groups = {
            match = "FlashMatch",
            current = "FlashCurrent",
            backdrop = "FlashBackdrop",
            label = "FlashLabel",
        },
    },
    modes = {
        search = {
            enabled = true,
            highlight = { backdrop = false },
            jump = { history = true, register = true, nohlsearch = true },
        },
        char = {
            enabled = true,
            config = function(opts)
                opts.autohide = opts.autohide or (vim.fn.mode(true):find("no") and vim.v.operator == "y")
                opts.jump_labels = opts.jump_labels
                    and vim.v.count == 0
                    and vim.fn.reg_executing() == ""
                    and vim.fn.reg_recording() == ""
            end,
            autohide = false,
            jump_labels = false,
            multi_line = true,
            label = { exclude = "hjkliardc" },
            keys = { "f", "F", "t", "T", ";", "," },
            char_actions = function(motion)
                return {
                    [";"] = "next",
                    [","] = "prev",
                    [motion:lower()] = "next",
                    [motion:upper()] = "prev",
                }
            end,
            search = { wrap = false },
            highlight = { backdrop = true },
            jump = { register = false },
        },
        treesitter = {
            labels = "abcdefghijklmnopqrstuvwxyz",
            jump = { pos = "range" },
            search = { incremental = false },
            label = { before = true, after = true, style = "inline" },
            highlight = {
                backdrop = false,
                matches = false,
            },
        },
        treesitter_search = {
            jump = { pos = "range" },
            search = { multi_window = true, wrap = true, incremental = false },
            remote_op = { restore = true },
            label = { before = true, after = true, style = "inline" },
        },
        remote = {
            remote_op = { restore = true, motion = true },
        },
    },
})

-- ============================================================================
-- FLASH KEYMAPS
-- ============================================================================

-- Flash keymaps
vim.keymap.set({ "n", "x", "o" }, "s", function()
    require("flash").jump()
end, { desc = "Flash" })

vim.keymap.set({ "n", "x", "o" }, "S", function()
    require("flash").treesitter()
end, { desc = "Flash Treesitter" })

vim.keymap.set("o", "r", function()
    require("flash").remote()
end, { desc = "Remote Flash" })

vim.keymap.set({ "o", "x" }, "R", function()
    require("flash").treesitter_search()
end, { desc = "Treesitter Search" })

vim.keymap.set("c", "<c-s>", function()
    require("flash").toggle()
end, { desc = "Toggle Flash Search" })

-- Additional useful flash keymaps
vim.keymap.set("n", "<leader>s", function()
    require("flash").jump()
end, { desc = "Flash jump" })

vim.keymap.set("n", "<leader>S", function()
    require("flash").treesitter()
end, { desc = "Flash treesitter" })
