-- ============================================================================
-- PHP/LARAVEL CONFIGURATION
-- ============================================================================
-- PHP and Laravel development configuration

-- Load Laravel.nvim dependencies first
vim.cmd('packadd nui.nvim')
vim.cmd('packadd promise-async')

-- Laravel.nvim configuration
require("laravel").setup({
    lsp_server = "phpactor", -- Use phpactor as LSP server
    -- Use snacks picker since you have snacks.nvim configured
    default_picker = "snacks",
    -- Alternatively, use telescope since you have extensive telescope setup
    -- default_picker = "telescope",
})

-- Blade-nav.nvim configuration
require("blade-nav").setup({
    -- This setting works for blink.cmp
    close_tag_on_complete = true, -- default: true
})

-- ============================================================================
-- LARAVEL KEYMAPS
-- ============================================================================

-- Artisan commands
vim.keymap.set('n', '<leader>aa', '<cmd>Laravel artisan<cr>', { desc = 'Artisan commands' })
vim.keymap.set('n', '<leader>ar', '<cmd>Laravel routes<cr>', { desc = 'Laravel routes' })
vim.keymap.set('n', '<leader>am', '<cmd>Laravel make<cr>', { desc = 'Laravel make' })

-- Laravel development
vim.keymap.set('n', '<leader>av', '<cmd>Laravel view_finder<cr>', { desc = 'View finder' })
vim.keymap.set('n', '<leader>ac', '<cmd>Laravel composer<cr>', { desc = 'Composer commands' })
vim.keymap.set('n', '<leader>at', '<cmd>Laravel tinker<cr>', { desc = 'Laravel Tinker' })

-- Route navigation (blade-nav provides this)
vim.keymap.set('n', '<leader>ao', '<cmd>Laravel route:open<cr>', { desc = 'Open route in browser' })
