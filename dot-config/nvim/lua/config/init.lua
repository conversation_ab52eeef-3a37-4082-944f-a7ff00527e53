-- ============================================================================
-- NEOVIM CORE CONFIGURATION LOADER
-- ============================================================================
-- This file loads all core configuration modules in the correct order

local M = {}

-- Load configuration modules in order
function M.setup()
    -- Load core options first
    require("config.options")

    -- Load basic keymaps (plugin-specific keymaps are loaded with their plugins)
    require("config.keymaps")

    -- Load autocommands
    require("config.autocommands")

    -- Load plugins and their configurations
    require("plugins").setup()
end

return M
